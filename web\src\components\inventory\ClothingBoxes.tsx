import React from 'react';
import InventorySlot from './InventorySlot';
import { useAppSelector } from '../../store';
import { selectLeftInventory } from '../../store/inventory';
import { getItemUrl } from '../../helpers';

const ClothingBoxes: React.FC = () => {
  const leftInventory = useAppSelector(selectLeftInventory);
  const isBusy = useAppSelector((state) => state.inventory.isBusy);

  // Define the clothing slots - using higher slot numbers to avoid conflict with hotbar (slots 1-5)
  const clothingSlots = [
    { slot: 41, label: 'Mask', icon: './images/bandage.png', placeholder: 'https://via.placeholder.com/128x128/FFFFFF/000000?text=MASK' },
    { slot: 42, label: 'Armor', icon: './images/armour.png', placeholder: 'https://via.placeholder.com/128x128/FFFFFF/000000?text=ARMOR' },
    { slot: 43, label: 'Bag', icon: './images/paperbag.png', placeholder: 'https://via.placeholder.com/128x128/FFFFFF/000000?text=BAG' }
  ];

  // Get the clothing items from the player inventory
  const getClothingItem = (slotNumber: number) => {
    return leftInventory.items.find(item => item.slot === slotNumber) || { slot: slotNumber };
  };

  return (
    <div className="clothing-boxes-container">
      {clothingSlots.map((clothingSlot) => (
        <div key={clothingSlot.slot} className="inventory-grid-wrapper" style={{ pointerEvents: isBusy ? 'none' : 'auto' }}>
          <div>
            <div className="clothing-header-wrapper">
              <img className="inventory-icon" src={clothingSlot.icon} alt={clothingSlot.label} />
              <p>{clothingSlot.label}</p>
            </div>
          </div>
          <div className="clothing-grid-container">
            <div
              className="inventory-slot clothing-slot"
              style={{
                backgroundImage: getClothingItem(clothingSlot.slot).name
                  ? `url(${getItemUrl(getClothingItem(clothingSlot.slot))})`
                  : `url(${clothingSlot.placeholder})`,
                opacity: getClothingItem(clothingSlot.slot).name ? 1 : 0.5,
                backgroundSize: 'contain',
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'center'
              }}
            >
              <InventorySlot
                item={getClothingItem(clothingSlot.slot)}
                inventoryType={leftInventory.type}
                inventoryGroups={leftInventory.groups}
                inventoryId={leftInventory.id}
              />
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ClothingBoxes;
