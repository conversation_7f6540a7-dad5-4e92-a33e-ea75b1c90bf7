import React from 'react';
import InventorySlot from './InventorySlot';
import { useAppSelector } from '../../store';
import { selectLeftInventory } from '../../store/inventory';

const ClothingBoxes: React.FC = () => {
  const leftInventory = useAppSelector(selectLeftInventory);
  const isBusy = useAppSelector((state) => state.inventory.isBusy);

  // Define the clothing slots - using higher slot numbers to avoid conflict with hotbar (slots 1-5)
  const clothingSlots = [
    { slot: 41, label: 'Mask', icon: './images/bandage.png' },
    { slot: 42, label: 'Armor', icon: './images/armour.png' },
    { slot: 43, label: 'Bag', icon: './images/paperbag.png' }
  ];

  // Get the clothing items from the player inventory
  const getClothingItem = (slotNumber: number) => {
    return leftInventory.items.find(item => item.slot === slotNumber) || { slot: slotNumber };
  };

  return (
    <div className="clothing-boxes-container">
      {clothingSlots.map((clothingSlot) => (
        <div key={clothingSlot.slot} className="inventory-grid-wrapper" style={{ pointerEvents: isBusy ? 'none' : 'auto' }}>
          <div>
            <div className="inventory-grid-header-wrapper">
              <img className="inventory-icon" src={clothingSlot.icon} alt={clothingSlot.label} />
              <p>{clothingSlot.label}</p>
            </div>
          </div>
          <div className="clothing-grid-container">
            <InventorySlot
              item={getClothingItem(clothingSlot.slot)}
              inventoryType={leftInventory.type}
              inventoryGroups={leftInventory.groups}
              inventoryId={leftInventory.id}
            />
          </div>
        </div>
      ))}
    </div>
  );
};

export default ClothingBoxes;
