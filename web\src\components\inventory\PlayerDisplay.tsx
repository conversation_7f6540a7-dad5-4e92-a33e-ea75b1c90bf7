import React from 'react';

const PlayerDisplay: React.FC = () => {
  return (
    <div className="player-display-container">
      <div className="player-display-wrapper">
        <div className="player-display-content">
          {/* Player model display area - matches the reference image */}
          <div className="player-avatar">
            <div style={{
              width: '200px',
              height: '300px',
              background: '#1a1d21',
              border: '1px solid #2a2d31',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              position: 'relative',
              boxShadow: 'inset 0px 0px 30px 5px rgba(16,16,18,1)'
            }}>
              {/* Character silhouette - more visible */}
              <div style={{
                width: '140px',
                height: '240px',
                background: 'linear-gradient(180deg, rgba(200,150,100,0.8) 0%, rgba(150,100,80,0.6) 50%, rgba(100,80,60,0.4) 100%)',
                borderRadius: '70px 70px 30px 30px',
                position: 'relative',
                opacity: 0.9
              }}>
                {/* Head */}
                <div style={{
                  width: '50px',
                  height: '50px',
                  background: 'rgba(200,150,100,0.9)',
                  borderRadius: '50%',
                  position: 'absolute',
                  top: '15px',
                  left: '50%',
                  transform: 'translateX(-50%)',
                  border: '1px solid rgba(255,255,255,0.1)'
                }}></div>

                {/* Body details */}
                <div style={{
                  width: '80px',
                  height: '120px',
                  background: 'rgba(100,80,60,0.6)',
                  position: 'absolute',
                  top: '70px',
                  left: '50%',
                  transform: 'translateX(-50%)',
                  borderRadius: '40px 40px 10px 10px'
                }}></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PlayerDisplay;
